#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADX持续性分析中文回复测试脚本
测试新增的中文回复功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_adx_chinese_reply():
    """测试ADX持续性分析的中文回复功能"""
    
    # 模拟MainWindow类的相关方法
    class MockMainWindow:
        def __init__(self):
            self.adx_threshold = 25
            self.current_adx = 41.96
            
        def log_trading(self, message, level='info'):
            print(f"[{level.upper()}] {message}")
            
        def generate_adx_persistence_chinese_reply(self, current_adx, plus_di_trend, minus_di_trend, adx_rising_periods):
            """
            生成ADX持续性分析的详细中文回复
            """
            try:
                # ADX强度描述
                if current_adx >= 50:
                    adx_strength_desc = "极强"
                    adx_emoji = "🔥"
                elif current_adx >= 40:
                    adx_strength_desc = "很强"
                    adx_emoji = "⚡"
                elif current_adx >= 25:
                    adx_strength_desc = "较强"
                    adx_emoji = "📈"
                else:
                    adx_strength_desc = "偏弱"
                    adx_emoji = "📊"

                # +DI趋势中文描述
                plus_di_desc_map = {
                    'rising': '上升',
                    'falling': '下降', 
                    'neutral': '中性'
                }
                plus_di_desc = plus_di_desc_map.get(plus_di_trend['direction'], '未知')

                # -DI趋势中文描述
                minus_di_desc_map = {
                    'rising': '上升',
                    'falling': '下降',
                    'neutral': '中性'
                }
                minus_di_desc = minus_di_desc_map.get(minus_di_trend['direction'], '未知')

                # 构建基础描述
                base_description = f"{adx_emoji} ADX持续性分析：趋势持续性不明确"
                
                # 详细分析
                detailed_analysis = []
                detailed_analysis.append(f"📊 ADX={current_adx:.2f}（{adx_strength_desc}趋势强度）")
                detailed_analysis.append(f"📈 +DI趋势={plus_di_desc}")
                detailed_analysis.append(f"📉 -DI趋势={minus_di_desc}")

                # 根据具体情况给出解读
                interpretation = []
                
                # ADX强度解读
                if current_adx >= 40:
                    interpretation.append("✅ ADX值显示市场存在强劲趋势")
                elif current_adx >= 25:
                    interpretation.append("⚠️ ADX值显示市场趋势适中")
                else:
                    interpretation.append("❌ ADX值偏低，市场可能处于震荡状态")

                # DI线组合解读
                if plus_di_trend['direction'] == 'neutral' and minus_di_trend['direction'] == 'falling':
                    interpretation.append("🔄 +DI保持中性而-DI下降，表明空头力量在减弱")
                    interpretation.append("💡 这种组合通常预示着市场可能从下跌转向盘整")
                    
                    if current_adx >= 40:
                        interpretation.append("⚡ 但ADX仍然很高，需要观察是否会出现方向性突破")
                        
                elif plus_di_trend['direction'] == 'neutral' and minus_di_trend['direction'] == 'rising':
                    interpretation.append("📉 +DI中性而-DI上升，空头力量在增强")
                    interpretation.append("⚠️ 市场可能面临进一步下跌压力")
                    
                elif plus_di_trend['direction'] == 'falling' and minus_di_trend['direction'] == 'neutral':
                    interpretation.append("📈 多头力量减弱而空头保持中性")
                    interpretation.append("🔄 市场可能从上涨转向盘整")

                # ADX变化趋势解读
                if adx_rising_periods >= 3:
                    interpretation.append(f"📊 ADX连续{adx_rising_periods}期上升，趋势强度在增加")
                elif adx_rising_periods > 0:
                    interpretation.append(f"📈 ADX近期有{adx_rising_periods}期上升")
                else:
                    interpretation.append("📉 ADX未显示明显上升趋势")

                # 交易建议
                trading_advice = []
                if current_adx >= 40:
                    if plus_di_trend['direction'] == 'neutral' and minus_di_trend['direction'] == 'falling':
                        trading_advice.append("💼 建议：观察多头是否能够重新发力")
                        trading_advice.append("🎯 关注点：+DI是否会转为上升趋势")
                    else:
                        trading_advice.append("💼 建议：高ADX环境下保持谨慎，等待明确方向信号")
                else:
                    trading_advice.append("💼 建议：ADX偏低时避免追涨杀跌，等待趋势明确")

                # 组合所有描述
                full_description_parts = [base_description]
                full_description_parts.extend(detailed_analysis)
                full_description_parts.append("\n🔍 市场解读：")
                full_description_parts.extend(interpretation)
                full_description_parts.append("\n💡 交易建议：")
                full_description_parts.extend(trading_advice)

                return "\n".join(full_description_parts)

            except Exception as e:
                self.log_trading(f"生成ADX中文回复失败: {str(e)}", level='error')
                return f"趋势持续性不明确：ADX={current_adx:.2f}, +DI趋势={plus_di_trend.get('direction', '未知')}, -DI趋势={minus_di_trend.get('direction', '未知')}"

        def get_adx_persistence_analysis_chinese(self, adx_value=None, plus_di_trend_direction=None, minus_di_trend_direction=None):
            """获取ADX持续性分析的中文回复（便捷方法）"""
            try:
                # 使用提供的值或当前值
                current_adx = adx_value if adx_value is not None else getattr(self, 'current_adx', 41.96)
                
                # 构建趋势信息
                plus_di_trend = {
                    'direction': plus_di_trend_direction or 'neutral',
                    'persistence_periods': 1,
                    'total_change': 0.5,
                    'avg_change_per_period': 0.5
                }
                
                minus_di_trend = {
                    'direction': minus_di_trend_direction or 'falling',
                    'persistence_periods': 2,
                    'total_change': 1.0,
                    'avg_change_per_period': 0.5
                }
                
                # 生成中文回复
                return self.generate_adx_persistence_chinese_reply(
                    current_adx, plus_di_trend, minus_di_trend, 1
                )
                
            except Exception as e:
                self.log_trading(f"获取ADX中文分析失败: {str(e)}", level='error')
                return f"ADX持续性分析：趋势持续性不明确：ADX={current_adx:.2f}, +DI趋势={plus_di_trend_direction or 'neutral'}, -DI趋势={minus_di_trend_direction or 'falling'}"

    # 创建测试实例
    mock_window = MockMainWindow()
    
    print("=" * 80)
    print("ADX持续性分析中文回复测试")
    print("=" * 80)
    
    # 测试场景1：您提供的数据 - ADX=41.96, +DI趋势=neutral, -DI趋势=falling
    print("\n【测试场景1】您的数据：ADX=41.96, +DI趋势=neutral, -DI趋势=falling")
    print("-" * 60)
    result1 = mock_window.get_adx_persistence_analysis_chinese(
        adx_value=41.96,
        plus_di_trend_direction='neutral',
        minus_di_trend_direction='falling'
    )
    print(result1)
    
    # 测试场景2：强势上涨趋势
    print("\n\n【测试场景2】强势上涨：ADX=45.0, +DI趋势=rising, -DI趋势=falling")
    print("-" * 60)
    result2 = mock_window.get_adx_persistence_analysis_chinese(
        adx_value=45.0,
        plus_di_trend_direction='rising',
        minus_di_trend_direction='falling'
    )
    print(result2)
    
    # 测试场景3：震荡市场
    print("\n\n【测试场景3】震荡市场：ADX=20.0, +DI趋势=neutral, -DI趋势=neutral")
    print("-" * 60)
    result3 = mock_window.get_adx_persistence_analysis_chinese(
        adx_value=20.0,
        plus_di_trend_direction='neutral',
        minus_di_trend_direction='neutral'
    )
    print(result3)
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    test_adx_chinese_reply()
