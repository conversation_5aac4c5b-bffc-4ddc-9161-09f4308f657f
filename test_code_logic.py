#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
限价单止盈止损预设功能代码逻辑测试

该脚本测试新增功能的代码逻辑，不启动GUI界面
"""

import sys
import os
import inspect

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_code_structure():
    """测试代码结构和方法是否正确添加"""
    print("🔍 测试代码结构...")
    
    try:
        # 导入主窗口模块
        import main_window
        
        # 检查MainWindow类是否存在
        if not hasattr(main_window, 'MainWindow'):
            print("❌ MainWindow类不存在")
            return False
        
        MainWindow = main_window.MainWindow
        
        # 检查新增的方法是否存在
        required_methods = [
            'on_order_type_changed',
            'on_limit_tpsl_toggled',
            '_set_tpsl_for_order',
            '_start_limit_order_monitor',
            '_cancel_all_orders_on_timeout'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(MainWindow, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的方法都已正确添加")
        
        # 检查方法签名
        print("🔍 检查方法签名...")
        
        # 检查 on_order_type_changed 方法
        method = getattr(MainWindow, 'on_order_type_changed')
        sig = inspect.signature(method)
        if len(sig.parameters) == 1:  # 只有self参数
            print("✅ on_order_type_changed 方法签名正确")
        else:
            print("❌ on_order_type_changed 方法签名不正确")
            return False
        
        # 检查 on_limit_tpsl_toggled 方法
        method = getattr(MainWindow, 'on_limit_tpsl_toggled')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        if len(params) == 2 and params[1] == 'checked':  # self 和 checked 参数
            print("✅ on_limit_tpsl_toggled 方法签名正确")
        else:
            print("❌ on_limit_tpsl_toggled 方法签名不正确")
            return False
        
        # 检查 _set_tpsl_for_order 方法
        method = getattr(MainWindow, '_set_tpsl_for_order')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        expected_params = ['self', 'swap_symbol', 'side', 'quantity', 'tp_price_str', 'sl_price_str']
        if params == expected_params:
            print("✅ _set_tpsl_for_order 方法签名正确")
        else:
            print(f"❌ _set_tpsl_for_order 方法签名不正确，期望: {expected_params}, 实际: {params}")
            return False
        
        # 检查 _start_limit_order_monitor 方法
        method = getattr(MainWindow, '_start_limit_order_monitor')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        expected_params = ['self', 'order_id', 'swap_symbol', 'side', 'quantity', 'tp_price_str', 'sl_price_str', 'entry_price']
        if params == expected_params:
            print("✅ _start_limit_order_monitor 方法签名正确")
        else:
            print(f"❌ _start_limit_order_monitor 方法签名不正确，期望: {expected_params}, 实际: {params}")
            return False

        # 检查 _cancel_all_orders_on_timeout 方法
        method = getattr(MainWindow, '_cancel_all_orders_on_timeout')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        expected_params = ['self', 'swap_symbol', 'original_order_id']
        if params == expected_params:
            print("✅ _cancel_all_orders_on_timeout 方法签名正确")
        else:
            print(f"❌ _cancel_all_orders_on_timeout 方法签名不正确，期望: {expected_params}, 实际: {params}")
            return False
        
        print("✅ 所有方法签名检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 代码结构测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件是否正确添加到代码中"""
    print("🔍 测试UI组件...")
    
    try:
        # 读取main_window.py文件内容
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键UI组件是否存在于代码中
        ui_components = [
            'enable_limit_tpsl_checkbox',
            'limit_tpsl_widget',
            'limit_tpsl_layout',
            'QCheckBox',
            '限价单同时设置止盈止损'
        ]
        
        missing_components = []
        for component in ui_components:
            if component not in content:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 代码中缺少UI组件: {missing_components}")
            return False
        else:
            print("✅ 所有UI组件都已正确添加到代码中")
        
        # 检查事件连接是否正确
        event_connections = [
            'limit_order_radio.toggled.connect(self.on_order_type_changed)',
            'enable_limit_tpsl_checkbox.toggled.connect(self.on_limit_tpsl_toggled)'
        ]
        
        missing_connections = []
        for connection in event_connections:
            if connection not in content:
                missing_connections.append(connection)
        
        if missing_connections:
            print(f"❌ 代码中缺少事件连接: {missing_connections}")
            return False
        else:
            print("✅ 所有事件连接都已正确添加")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {str(e)}")
        return False

def test_place_ai_order_modifications():
    """测试place_ai_order方法的修改是否正确"""
    print("🔍 测试place_ai_order方法修改...")
    
    try:
        # 读取main_window.py文件内容
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改是否存在
        key_modifications = [
            'limit_tpsl_enabled',
            'enable_limit_tpsl_checkbox',
            '_start_limit_order_monitor',
            '_set_tpsl_for_order',
            '_cancel_all_orders_on_timeout',
            '限价单止盈止损预设已启用',
            'max_checks = 360',  # 检查1小时监控设置
            '监控超时（1小时）'
        ]
        
        missing_modifications = []
        for modification in key_modifications:
            if modification not in content:
                missing_modifications.append(modification)
        
        if missing_modifications:
            print(f"❌ place_ai_order方法中缺少关键修改: {missing_modifications}")
            return False
        else:
            print("✅ place_ai_order方法修改正确")
        
        return True
        
    except Exception as e:
        print(f"❌ place_ai_order方法测试失败: {str(e)}")
        return False

def test_import_statements():
    """测试导入语句是否正确"""
    print("🔍 测试导入语句...")
    
    try:
        # 读取main_window.py文件内容
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查QCheckBox导入是否存在
        if 'from PyQt6.QtWidgets import QCheckBox' in content or 'QCheckBox' in content:
            print("✅ QCheckBox导入正确")
        else:
            print("❌ 缺少QCheckBox导入")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入语句测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🎯 限价单止盈止损预设功能代码逻辑测试")
    print("=" * 60)
    
    tests = [
        ("代码结构测试", test_code_structure),
        ("UI组件测试", test_ui_components), 
        ("place_ai_order修改测试", test_place_ai_order_modifications),
        ("导入语句测试", test_import_statements)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！限价单止盈止损预设功能已正确实现")
        return True
    else:
        print("⚠️  部分测试失败，请检查代码实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
