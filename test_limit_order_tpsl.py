#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
限价单止盈止损预设功能测试脚本

该脚本用于测试新增的限价单止盈止损预设功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_limit_order_tpsl_ui():
    """测试限价单止盈止损预设UI功能"""
    print("🚀 开始测试限价单止盈止损预设功能...")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入主窗口
        from main_window import MainWindow
        
        # 创建主窗口实例
        window = MainWindow()
        
        print("✅ 主窗口创建成功")
        
        # 检查新增的UI组件是否存在
        ui_components = [
            'limit_order_radio',
            'enable_limit_tpsl_checkbox', 
            'limit_tpsl_widget'
        ]
        
        missing_components = []
        for component in ui_components:
            if not hasattr(window, component):
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 缺少UI组件: {missing_components}")
            return False
        else:
            print("✅ 所有UI组件都已正确创建")
        
        # 检查新增的方法是否存在
        methods = [
            'on_order_type_changed',
            'on_limit_tpsl_toggled',
            '_set_tpsl_for_order',
            '_start_limit_order_monitor'
        ]
        
        missing_methods = []
        for method in methods:
            if not hasattr(window, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有新增方法都已正确实现")
        
        # 测试UI交互
        print("🔄 测试UI交互...")
        
        # 测试选择限价单时显示止盈止损预设选项
        window.limit_order_radio.setChecked(True)
        window.on_order_type_changed()
        
        if window.limit_tpsl_widget.isVisible():
            print("✅ 选择限价单时正确显示止盈止损预设选项")
        else:
            print("❌ 选择限价单时未显示止盈止损预设选项")
            return False
        
        # 测试选择市价单时隐藏止盈止损预设选项
        window.market_order_radio.setChecked(True)
        window.on_order_type_changed()
        
        if not window.limit_tpsl_widget.isVisible():
            print("✅ 选择市价单时正确隐藏止盈止损预设选项")
        else:
            print("❌ 选择市价单时未隐藏止盈止损预设选项")
            return False
        
        # 测试止盈止损预设功能切换
        window.enable_limit_tpsl_checkbox.setChecked(True)
        window.on_limit_tpsl_toggled(True)
        print("✅ 止盈止损预设功能启用测试通过")
        
        window.enable_limit_tpsl_checkbox.setChecked(False)
        window.on_limit_tpsl_toggled(False)
        print("✅ 止盈止损预设功能禁用测试通过")
        
        # 显示窗口进行手动测试
        window.show()
        print("✅ 窗口已显示，可以进行手动测试")
        print("📋 手动测试步骤:")
        print("   1. 选择'限价单'单选按钮，观察是否显示止盈止损预设选项")
        print("   2. 选择'市价单'单选按钮，观察是否隐藏止盈止损预设选项")
        print("   3. 在限价单模式下，切换'限价单同时设置止盈止损'复选框")
        print("   4. 查看交易日志中的相关信息")
        print("   5. 关闭窗口结束测试")
        
        # 设置定时器自动关闭（可选）
        def auto_close():
            print("⏰ 自动关闭测试窗口")
            window.close()
            app.quit()
        
        # 30秒后自动关闭（可以注释掉这行进行手动测试）
        # QTimer.singleShot(30000, auto_close)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_functionality_logic():
    """测试功能逻辑（不启动GUI）"""
    print("🔍 测试功能逻辑...")
    
    try:
        # 导入主窗口类
        from main_window import MainWindow
        
        # 创建一个虚拟的应用程序上下文
        app = QApplication([])
        
        # 创建主窗口实例（但不显示）
        window = MainWindow()
        
        # 测试订单类型变化逻辑
        print("测试订单类型变化逻辑...")
        
        # 模拟选择限价单
        window.limit_order_radio.setChecked(True)
        window.on_order_type_changed()
        
        # 模拟选择市价单
        window.market_order_radio.setChecked(True)
        window.on_order_type_changed()
        
        # 测试止盈止损预设切换逻辑
        print("测试止盈止损预设切换逻辑...")
        window.on_limit_tpsl_toggled(True)
        window.on_limit_tpsl_toggled(False)
        
        print("✅ 功能逻辑测试通过")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 功能逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 限价单止盈止损预设功能测试")
    print("=" * 60)
    
    # 首先测试功能逻辑
    logic_test_passed = test_functionality_logic()
    
    if logic_test_passed:
        print("\n" + "=" * 60)
        print("🖥️  启动UI测试...")
        print("=" * 60)
        
        # 然后测试UI功能
        ui_test_result = test_limit_order_tpsl_ui()
        
        if ui_test_result == 0:  # QApplication.exec() 返回0表示正常退出
            print("✅ 所有测试通过！")
        else:
            print("⚠️  UI测试可能存在问题")
    else:
        print("❌ 功能逻辑测试失败，跳过UI测试")
    
    print("=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
